// Blog types and utilities

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    bio?: string;
  };
  featuredImage?: string;
  tags: string[];
  category: string;
  publishedAt: string;
  updatedAt: string;
  readingTime: number; // in minutes
  isPublished: boolean;
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  views: number;
  likes: number;
}

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  color: string;
  postCount: number;
}

export interface BlogTag {
  id: string;
  name: string;
  slug: string;
  postCount: number;
}

export interface BlogComment {
  id: string;
  postId: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  content: string;
  createdAt: string;
  updatedAt: string;
  parentId?: string; // for nested comments
  isApproved: boolean;
}

// Utility functions
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const words = content.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}

export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export function getRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
  return `${Math.floor(diffInSeconds / 31536000)} years ago`;
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

// Mock data for development
export const mockBlogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'The Ultimate Guide to Modern Interior Design',
    slug: 'ultimate-guide-modern-interior-design',
    excerpt: 'Discover the principles of modern interior design and how to create stunning contemporary spaces that reflect your personal style.',
    content: `# The Ultimate Guide to Modern Interior Design

Modern interior design has become increasingly popular in recent years, and for good reason. This design style emphasizes clean lines, minimalism, and functionality while creating spaces that feel both sophisticated and comfortable.

## What is Modern Interior Design?

Modern interior design is characterized by:
- Clean, straight lines
- Minimal ornamentation
- Neutral color palettes
- Open floor plans
- Natural materials
- Functional furniture

## Key Elements of Modern Design

### 1. Color Palette
Modern design typically uses neutral colors like white, gray, black, and beige as the foundation, with occasional pops of bold color for accent.

### 2. Furniture
Furniture in modern design is functional and streamlined, often featuring geometric shapes and clean lines.

### 3. Materials
Natural materials like wood, stone, and metal are commonly used, along with man-made materials like glass and steel.

## Tips for Creating a Modern Space

1. **Declutter**: Remove unnecessary items and keep only what you need
2. **Choose quality over quantity**: Invest in fewer, high-quality pieces
3. **Embrace negative space**: Don't feel the need to fill every corner
4. **Mix textures**: Combine smooth and rough textures for visual interest
5. **Add plants**: Bring nature indoors with carefully chosen plants

Modern interior design is about creating spaces that are both beautiful and functional, where every element serves a purpose while contributing to the overall aesthetic.`,
    author: {
      id: '1',
      name: 'Sarah Johnson',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      bio: 'Interior design expert with 10+ years of experience in modern and contemporary design.'
    },
    featuredImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=400&fit=crop',
    tags: ['modern', 'interior-design', 'minimalism', 'home-decor'],
    category: 'Design Guides',
    publishedAt: '2024-08-08T10:00:00Z',
    updatedAt: '2024-08-08T10:00:00Z',
    readingTime: 5,
    isPublished: true,
    seo: {
      metaTitle: 'The Ultimate Guide to Modern Interior Design | Altora Design',
      metaDescription: 'Learn the principles of modern interior design and create stunning contemporary spaces. Expert tips and insights from professional designers.',
      keywords: ['modern interior design', 'contemporary design', 'minimalist decor', 'home design']
    },
    views: 1250,
    likes: 89
  },
  {
    id: '2',
    title: 'Sustainable Design: Eco-Friendly Interior Solutions',
    slug: 'sustainable-design-eco-friendly-interior-solutions',
    excerpt: 'Learn how to create beautiful interiors while being environmentally conscious. Explore sustainable materials and eco-friendly design practices.',
    content: `# Sustainable Design: Eco-Friendly Interior Solutions

As environmental awareness grows, more homeowners are seeking ways to create beautiful spaces while minimizing their ecological footprint. Sustainable interior design offers the perfect solution.

## What is Sustainable Interior Design?

Sustainable design focuses on:
- Using eco-friendly materials
- Reducing waste
- Improving energy efficiency
- Supporting local artisans
- Creating long-lasting designs

## Eco-Friendly Materials

### Reclaimed Wood
Using reclaimed wood reduces deforestation and gives new life to old materials.

### Bamboo
Fast-growing and renewable, bamboo is an excellent alternative to traditional hardwood.

### Recycled Materials
From recycled glass countertops to repurposed furniture, these materials reduce waste.

## Energy-Efficient Design

- LED lighting
- Smart home systems
- Proper insulation
- Natural lighting optimization

Sustainable design proves that you don't have to sacrifice style for environmental responsibility.`,
    author: {
      id: '2',
      name: 'Michael Chen',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      bio: 'Sustainable design advocate and certified green building professional.'
    },
    featuredImage: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=400&fit=crop',
    tags: ['sustainable', 'eco-friendly', 'green-design', 'environment'],
    category: 'Sustainability',
    publishedAt: '2024-08-06T14:30:00Z',
    updatedAt: '2024-08-06T14:30:00Z',
    readingTime: 4,
    isPublished: true,
    seo: {
      metaTitle: 'Sustainable Interior Design: Eco-Friendly Solutions | Altora',
      metaDescription: 'Discover sustainable interior design practices and eco-friendly materials for environmentally conscious home decoration.',
      keywords: ['sustainable design', 'eco-friendly interiors', 'green design', 'sustainable materials']
    },
    views: 890,
    likes: 67
  },
  {
    id: '3',
    title: 'Small Space, Big Style: Maximizing Tiny Homes',
    slug: 'small-space-big-style-maximizing-tiny-homes',
    excerpt: 'Transform small spaces into stylish, functional homes with clever design tricks and space-saving solutions.',
    content: `# Small Space, Big Style: Maximizing Tiny Homes

Living in a small space doesn't mean sacrificing style or functionality. With the right design strategies, even the tiniest homes can feel spacious and luxurious.

## Space-Saving Strategies

### Vertical Storage
Use wall-mounted shelves and tall furniture to maximize vertical space.

### Multi-Functional Furniture
Choose pieces that serve multiple purposes, like storage ottomans or expandable dining tables.

### Light Colors
Light colors reflect light and make spaces feel larger.

### Mirrors
Strategic mirror placement can double the visual space of a room.

## Design Tips for Small Spaces

1. **Keep it simple**: Avoid clutter and choose a cohesive color scheme
2. **Use natural light**: Keep windows unobstructed
3. **Create zones**: Define different areas for different activities
4. **Think outside the box**: Use unconventional storage solutions

Small spaces offer unique opportunities for creative design solutions.`,
    author: {
      id: '3',
      name: 'Emma Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      bio: 'Small space design specialist and tiny home enthusiast.'
    },
    featuredImage: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=400&fit=crop',
    tags: ['small-spaces', 'tiny-homes', 'space-saving', 'apartment-design'],
    category: 'Small Spaces',
    publishedAt: '2024-08-04T09:15:00Z',
    updatedAt: '2024-08-04T09:15:00Z',
    readingTime: 3,
    isPublished: true,
    seo: {
      metaTitle: 'Small Space Design: Maximizing Tiny Homes | Altora Design',
      metaDescription: 'Learn how to maximize small spaces with clever design tricks and space-saving solutions for tiny homes and apartments.',
      keywords: ['small space design', 'tiny homes', 'apartment design', 'space saving']
    },
    views: 1100,
    likes: 78
  }
];

export const mockBlogCategories: BlogCategory[] = [
  {
    id: '1',
    name: 'Design Guides',
    slug: 'design-guides',
    description: 'Comprehensive guides to interior design styles and techniques',
    color: '#2dd4bf',
    postCount: 12
  },
  {
    id: '2',
    name: 'Sustainability',
    slug: 'sustainability',
    description: 'Eco-friendly design practices and sustainable materials',
    color: '#10b981',
    postCount: 8
  },
  {
    id: '3',
    name: 'Small Spaces',
    slug: 'small-spaces',
    description: 'Design solutions for apartments and tiny homes',
    color: '#f59e0b',
    postCount: 6
  },
  {
    id: '4',
    name: 'Trends',
    slug: 'trends',
    description: 'Latest trends in interior design and home decor',
    color: '#8b5cf6',
    postCount: 10
  }
];

export const mockBlogTags: BlogTag[] = [
  { id: '1', name: 'Modern', slug: 'modern', postCount: 15 },
  { id: '2', name: 'Minimalism', slug: 'minimalism', postCount: 12 },
  { id: '3', name: 'Sustainable', slug: 'sustainable', postCount: 8 },
  { id: '4', name: 'Small Spaces', slug: 'small-spaces', postCount: 6 },
  { id: '5', name: 'Color Theory', slug: 'color-theory', postCount: 9 },
  { id: '6', name: 'Lighting', slug: 'lighting', postCount: 7 },
  { id: '7', name: 'Furniture', slug: 'furniture', postCount: 11 },
  { id: '8', name: 'DIY', slug: 'diy', postCount: 5 }
];
