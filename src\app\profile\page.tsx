"use client";
import React, { useState } from 'react';
import Link from 'next/link';

// Mock user data - replace with actual user data from Supabase
const mockUser = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  joinedDate: '2024-01-15',
  subscription: 'Pro',
  designsCreated: 24,
  favoriteStyles: ['Modern', 'Minimalist', 'Scandinavian']
};

// Mock design history
const mockDesigns = [
  {
    id: '1',
    title: 'Living Room Redesign',
    style: 'Modern',
    roomType: 'Living Room',
    createdAt: '2024-08-05',
    thumbnail: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',
    status: 'completed'
  },
  {
    id: '2',
    title: 'Bedroom Makeover',
    style: 'Scandinavian',
    roomType: 'Bedroom',
    createdAt: '2024-08-03',
    thumbnail: 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=300&h=200&fit=crop',
    status: 'completed'
  },
  {
    id: '3',
    title: 'Kitchen Update',
    style: 'Minimalist',
    roomType: 'Kitchen',
    createdAt: '2024-08-01',
    thumbnail: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop',
    status: 'in_progress'
  }
];

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'designs' | 'settings'>('overview');

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-8">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
            <img
              src={mockUser.avatar}
              alt={mockUser.name}
              className="w-24 h-24 rounded-full border-4 border-teal-500"
            />
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-white mb-2">{mockUser.name}</h1>
              <p className="text-gray-400 mb-2">{mockUser.email}</p>
              <div className="flex flex-wrap gap-4 text-sm">
                <span className="bg-teal-600 text-white px-3 py-1 rounded-full">
                  {mockUser.subscription} Plan
                </span>
                <span className="text-gray-400">
                  Joined {new Date(mockUser.joinedDate).toLocaleDateString()}
                </span>
                <span className="text-gray-400">
                  {mockUser.designsCreated} designs created
                </span>
              </div>
            </div>
            <Link
              href="/design/new"
              className="bg-teal-600 hover:bg-teal-500 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Create New Design
            </Link>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 mb-8">
          <div className="flex border-b border-gray-800">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'designs', label: 'My Designs' },
              { id: 'settings', label: 'Settings' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-6 py-4 font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-teal-400 border-b-2 border-teal-400'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          <div className="p-8">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h3 className="text-white font-semibold mb-2">Designs Created</h3>
                    <p className="text-3xl font-bold text-teal-400">{mockUser.designsCreated}</p>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h3 className="text-white font-semibold mb-2">Favorite Styles</h3>
                    <div className="flex flex-wrap gap-2">
                      {mockUser.favoriteStyles.map((style) => (
                        <span key={style} className="bg-gray-700 text-gray-300 px-2 py-1 rounded text-sm">
                          {style}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h3 className="text-white font-semibold mb-2">Subscription</h3>
                    <p className="text-xl font-semibold text-white">{mockUser.subscription} Plan</p>
                    <Link href="/billing" className="text-teal-400 hover:text-teal-300 text-sm">
                      Manage billing →
                    </Link>
                  </div>
                </div>

                {/* Recent Designs */}
                <div>
                  <h3 className="text-white font-semibold text-xl mb-4">Recent Designs</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {mockDesigns.slice(0, 3).map((design) => (
                      <div key={design.id} className="bg-gray-800/50 rounded-lg overflow-hidden">
                        <img
                          src={design.thumbnail}
                          alt={design.title}
                          className="w-full h-48 object-cover"
                        />
                        <div className="p-4">
                          <h4 className="text-white font-medium mb-2">{design.title}</h4>
                          <div className="flex justify-between items-center text-sm text-gray-400">
                            <span>{design.style} • {design.roomType}</span>
                            <span>{new Date(design.createdAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Designs Tab */}
            {activeTab === 'designs' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-white font-semibold text-xl">All Designs</h3>
                  <div className="flex gap-2">
                    <select className="bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 text-sm">
                      <option>All Styles</option>
                      <option>Modern</option>
                      <option>Minimalist</option>
                      <option>Scandinavian</option>
                    </select>
                    <select className="bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 text-sm">
                      <option>All Rooms</option>
                      <option>Living Room</option>
                      <option>Bedroom</option>
                      <option>Kitchen</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {mockDesigns.map((design) => (
                    <div key={design.id} className="bg-gray-800/50 rounded-lg overflow-hidden">
                      <img
                        src={design.thumbnail}
                        alt={design.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="text-white font-medium">{design.title}</h4>
                          <span className={`px-2 py-1 rounded text-xs ${
                            design.status === 'completed' 
                              ? 'bg-green-600 text-white' 
                              : 'bg-yellow-600 text-white'
                          }`}>
                            {design.status === 'completed' ? 'Completed' : 'In Progress'}
                          </span>
                        </div>
                        <div className="flex justify-between items-center text-sm text-gray-400 mb-3">
                          <span>{design.style} • {design.roomType}</span>
                          <span>{new Date(design.createdAt).toLocaleDateString()}</span>
                        </div>
                        <div className="flex gap-2">
                          <button className="flex-1 bg-teal-600 hover:bg-teal-500 text-white py-2 px-3 rounded text-sm transition-colors">
                            View
                          </button>
                          <button className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded text-sm transition-colors">
                            Edit
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Settings Tab */}
            {activeTab === 'settings' && (
              <div className="space-y-8">
                {/* Profile Settings */}
                <div>
                  <h3 className="text-white font-semibold text-xl mb-4">Profile Settings</h3>
                  <div className="bg-gray-800/50 rounded-lg p-6 space-y-4">
                    <div>
                      <label className="block text-white font-medium mb-2">Name</label>
                      <input
                        type="text"
                        defaultValue={mockUser.name}
                        className="w-full bg-gray-700 text-white border border-gray-600 rounded px-3 py-2"
                      />
                    </div>
                    <div>
                      <label className="block text-white font-medium mb-2">Email</label>
                      <input
                        type="email"
                        defaultValue={mockUser.email}
                        className="w-full bg-gray-700 text-white border border-gray-600 rounded px-3 py-2"
                      />
                    </div>
                    <button className="bg-teal-600 hover:bg-teal-500 text-white px-6 py-2 rounded transition-colors">
                      Save Changes
                    </button>
                  </div>
                </div>

                {/* Preferences */}
                <div>
                  <h3 className="text-white font-semibold text-xl mb-4">Preferences</h3>
                  <div className="bg-gray-800/50 rounded-lg p-6 space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-white">Email notifications</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white">Marketing emails</span>
                      <input type="checkbox" className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white">Design completion alerts</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                  </div>
                </div>

                {/* Account Actions */}
                <div>
                  <h3 className="text-white font-semibold text-xl mb-4">Account</h3>
                  <div className="bg-gray-800/50 rounded-lg p-6 space-y-4">
                    <Link
                      href="/billing"
                      className="block bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors text-center"
                    >
                      Manage Subscription
                    </Link>
                    <button className="w-full bg-red-600 hover:bg-red-500 text-white px-4 py-2 rounded transition-colors">
                      Delete Account
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
