"use client";
import React, { useState, useMemo } from 'react';
import Link from 'next/link';
import { mockBlogPosts, mockBlogCategories, mockBlogTags, formatDate, getRelativeTime, truncateText } from '@/lib/blog';

export default function BlogPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTag, setSelectedTag] = useState('all');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest');

  // Filter and sort posts
  const filteredPosts = useMemo(() => {
    let posts = [...mockBlogPosts];

    // Filter by search query
    if (searchQuery) {
      posts = posts.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      posts = posts.filter(post => post.category === selectedCategory);
    }

    // Filter by tag
    if (selectedTag !== 'all') {
      posts = posts.filter(post => post.tags.includes(selectedTag));
    }

    // Sort posts
    posts.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
        case 'oldest':
          return new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime();
        case 'popular':
          return b.views - a.views;
        default:
          return 0;
      }
    });

    return posts;
  }, [searchQuery, selectedCategory, selectedTag, sortBy]);

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Design Blog
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Discover the latest trends, tips, and insights in interior design. 
            From modern minimalism to sustainable living, explore ideas that inspire.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <label className="block text-white font-medium mb-2">Search</label>
              <input
                type="text"
                placeholder="Search posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500"
              />
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-white font-medium mb-2">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500"
              >
                <option value="all">All Categories</option>
                {mockBlogCategories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Tag Filter */}
            <div>
              <label className="block text-white font-medium mb-2">Tag</label>
              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500"
              >
                <option value="all">All Tags</option>
                {mockBlogTags.map(tag => (
                  <option key={tag.id} value={tag.slug}>
                    {tag.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <div>
              <label className="block text-white font-medium mb-2">Sort By</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="popular">Most Popular</option>
              </select>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-400">
            Showing {filteredPosts.length} of {mockBlogPosts.length} posts
          </p>
        </div>

        {/* Blog Posts Grid */}
        {filteredPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {filteredPosts.map((post) => (
              <article key={post.id} className="bg-gray-900/50 rounded-2xl border border-gray-800 overflow-hidden hover:border-teal-500 transition-all duration-300 group">
                {/* Featured Image */}
                {post.featuredImage && (
                  <div className="relative overflow-hidden">
                    <img
                      src={post.featuredImage}
                      alt={post.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 left-4">
                      <span 
                        className="px-3 py-1 rounded-full text-xs font-medium text-white"
                        style={{ backgroundColor: mockBlogCategories.find(cat => cat.name === post.category)?.color || '#6b7280' }}
                      >
                        {post.category}
                      </span>
                    </div>
                  </div>
                )}

                <div className="p-6">
                  {/* Meta Info */}
                  <div className="flex items-center gap-4 text-sm text-gray-400 mb-3">
                    <div className="flex items-center gap-2">
                      <img
                        src={post.author.avatar}
                        alt={post.author.name}
                        className="w-6 h-6 rounded-full"
                      />
                      <span>{post.author.name}</span>
                    </div>
                    <span>•</span>
                    <span>{getRelativeTime(post.publishedAt)}</span>
                    <span>•</span>
                    <span>{post.readingTime} min read</span>
                  </div>

                  {/* Title */}
                  <h2 className="text-xl font-bold text-white mb-3 group-hover:text-teal-400 transition-colors">
                    <Link href={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </h2>

                  {/* Excerpt */}
                  <p className="text-gray-300 mb-4 line-clamp-3">
                    {truncateText(post.excerpt, 120)}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-400">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                        </svg>
                        {post.views}
                      </span>
                      <span className="flex items-center gap-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd"/>
                        </svg>
                        {post.likes}
                      </span>
                    </div>
                    <Link
                      href={`/blog/${post.slug}`}
                      className="text-teal-400 hover:text-teal-300 font-medium"
                    >
                      Read more →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg">No posts found matching your criteria.</p>
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
                setSelectedTag('all');
              }}
              className="mt-4 bg-teal-600 hover:bg-teal-500 text-white px-6 py-2 rounded transition-colors"
            >
              Clear Filters
            </button>
          </div>
        )}

        {/* Sidebar with Categories and Popular Tags */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Categories */}
          <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6">
            <h3 className="text-white font-semibold text-xl mb-4">Categories</h3>
            <div className="space-y-3">
              {mockBlogCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.name)}
                  className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                    selectedCategory === category.name
                      ? 'bg-teal-600 text-white'
                      : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                  }`}
                >
                  <span className="flex items-center gap-3">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    {category.name}
                  </span>
                  <span className="text-sm">{category.postCount}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Popular Tags */}
          <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6">
            <h3 className="text-white font-semibold text-xl mb-4">Popular Tags</h3>
            <div className="flex flex-wrap gap-2">
              {mockBlogTags.map((tag) => (
                <button
                  key={tag.id}
                  onClick={() => setSelectedTag(tag.slug)}
                  className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                    selectedTag === tag.slug
                      ? 'bg-teal-600 text-white'
                      : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                  }`}
                >
                  #{tag.name} ({tag.postCount})
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
