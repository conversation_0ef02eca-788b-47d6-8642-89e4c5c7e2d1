"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import { mockBlogPosts, formatDate, getRelativeTime } from '@/lib/blog';

export default function AdminBlogPage() {
  const [posts, setPosts] = useState(mockBlogPosts);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft'>('all');

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.author.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'published' && post.isPublished) ||
                         (statusFilter === 'draft' && !post.isPublished);
    
    return matchesSearch && matchesStatus;
  });

  const handleDelete = (postId: string) => {
    if (confirm('Are you sure you want to delete this blog post?')) {
      setPosts(prev => prev.filter(p => p.id !== postId));
    }
  };

  const handleToggleStatus = (postId: string) => {
    setPosts(prev => prev.map(p => 
      p.id === postId 
        ? { ...p, isPublished: !p.isPublished }
        : p
    ));
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Blog Management</h1>
            <nav className="text-sm text-gray-400">
              <Link href="/admin" className="hover:text-white">Admin</Link>
              <span className="mx-2">•</span>
              <span className="text-white">Blog</span>
            </nav>
          </div>
          <Link
            href="/admin/blog/create"
            className="bg-teal-600 hover:bg-teal-500 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Create New Post
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-900/50 rounded-lg border border-gray-800 p-6">
            <h3 className="text-gray-400 text-sm font-medium">Total Posts</h3>
            <p className="text-3xl font-bold text-white">{posts.length}</p>
          </div>
          <div className="bg-gray-900/50 rounded-lg border border-gray-800 p-6">
            <h3 className="text-gray-400 text-sm font-medium">Published</h3>
            <p className="text-3xl font-bold text-green-400">
              {posts.filter(p => p.isPublished).length}
            </p>
          </div>
          <div className="bg-gray-900/50 rounded-lg border border-gray-800 p-6">
            <h3 className="text-gray-400 text-sm font-medium">Drafts</h3>
            <p className="text-3xl font-bold text-yellow-400">
              {posts.filter(p => !p.isPublished).length}
            </p>
          </div>
          <div className="bg-gray-900/50 rounded-lg border border-gray-800 p-6">
            <h3 className="text-gray-400 text-sm font-medium">Total Views</h3>
            <p className="text-3xl font-bold text-teal-400">
              {posts.reduce((sum, p) => sum + p.views, 0).toLocaleString()}
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-white font-medium mb-2">Search</label>
              <input
                type="text"
                placeholder="Search posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500"
              />
            </div>
            <div>
              <label className="block text-white font-medium mb-2">Status</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500"
              >
                <option value="all">All Posts</option>
                <option value="published">Published</option>
                <option value="draft">Drafts</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={() => {
                  setSearchQuery('');
                  setStatusFilter('all');
                }}
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Posts Table */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-800/50">
                <tr>
                  <th className="text-left text-white font-medium p-4">Post</th>
                  <th className="text-left text-white font-medium p-4">Author</th>
                  <th className="text-left text-white font-medium p-4">Category</th>
                  <th className="text-left text-white font-medium p-4">Status</th>
                  <th className="text-left text-white font-medium p-4">Views</th>
                  <th className="text-left text-white font-medium p-4">Date</th>
                  <th className="text-left text-white font-medium p-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPosts.map((post) => (
                  <tr key={post.id} className="border-t border-gray-800">
                    <td className="p-4">
                      <div className="flex items-center gap-3">
                        {post.featuredImage && (
                          <img
                            src={post.featuredImage}
                            alt={post.title}
                            className="w-12 h-12 object-cover rounded"
                          />
                        )}
                        <div>
                          <h3 className="text-white font-medium">{post.title}</h3>
                          <p className="text-gray-400 text-sm">{post.excerpt.substring(0, 60)}...</p>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <img
                          src={post.author.avatar}
                          alt={post.author.name}
                          className="w-8 h-8 rounded-full"
                        />
                        <span className="text-white">{post.author.name}</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <span className="text-gray-300">{post.category}</span>
                    </td>
                    <td className="p-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        post.isPublished 
                          ? 'bg-green-600 text-white' 
                          : 'bg-yellow-600 text-white'
                      }`}>
                        {post.isPublished ? 'Published' : 'Draft'}
                      </span>
                    </td>
                    <td className="p-4">
                      <span className="text-gray-300">{post.views.toLocaleString()}</span>
                    </td>
                    <td className="p-4">
                      <span className="text-gray-300">{getRelativeTime(post.publishedAt)}</span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <Link
                          href={`/blog/${post.slug}`}
                          className="text-teal-400 hover:text-teal-300 text-sm"
                          target="_blank"
                        >
                          View
                        </Link>
                        <Link
                          href={`/admin/blog/edit/${post.id}`}
                          className="text-blue-400 hover:text-blue-300 text-sm"
                        >
                          Edit
                        </Link>
                        <button
                          onClick={() => handleToggleStatus(post.id)}
                          className="text-yellow-400 hover:text-yellow-300 text-sm"
                        >
                          {post.isPublished ? 'Unpublish' : 'Publish'}
                        </button>
                        <button
                          onClick={() => handleDelete(post.id)}
                          className="text-red-400 hover:text-red-300 text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredPosts.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400">No posts found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
