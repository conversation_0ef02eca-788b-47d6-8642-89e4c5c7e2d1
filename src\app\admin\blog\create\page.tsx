"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import { mockBlogCategories, mockBlogTags, generateSlug, calculateReadingTime } from '@/lib/blog';

export default function CreateBlogPostPage() {
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    category: '',
    tags: [] as string[],
    featuredImage: '',
    metaTitle: '',
    metaDescription: '',
    keywords: '',
    isPublished: false
  });

  const [isPreview, setIsPreview] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
      
      // Auto-generate slug from title
      if (name === 'title') {
        setFormData(prev => ({ ...prev, slug: generateSlug(value) }));
      }
    }
  };

  const handleTagToggle = (tagSlug: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.includes(tagSlug)
        ? prev.tags.filter(t => t !== tagSlug)
        : [...prev.tags, tagSlug]
    }));
  };

  const handleSave = async (publish: boolean = false) => {
    setIsSaving(true);
    
    // Simulate API call
    try {
      const postData = {
        ...formData,
        isPublished: publish,
        readingTime: calculateReadingTime(formData.content),
        publishedAt: publish ? new Date().toISOString() : null,
        updatedAt: new Date().toISOString()
      };
      
      console.log('Saving blog post:', postData);
      
      // Mock API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert(publish ? 'Blog post published successfully!' : 'Blog post saved as draft!');
    } catch (error) {
      console.error('Error saving blog post:', error);
      alert('Error saving blog post. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const readingTime = calculateReadingTime(formData.content);

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Create New Blog Post</h1>
            <nav className="text-sm text-gray-400">
              <Link href="/admin" className="hover:text-white">Admin</Link>
              <span className="mx-2">•</span>
              <Link href="/admin/blog" className="hover:text-white">Blog</Link>
              <span className="mx-2">•</span>
              <span className="text-white">Create</span>
            </nav>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => setIsPreview(!isPreview)}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors"
            >
              {isPreview ? 'Edit' : 'Preview'}
            </button>
            <Link
              href="/admin/blog"
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors"
            >
              Cancel
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {!isPreview ? (
              <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 space-y-6">
                {/* Title */}
                <div>
                  <label className="block text-white font-medium mb-2">Title *</label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Enter blog post title..."
                    className="w-full bg-gray-800 text-white border border-gray-700 rounded px-4 py-3 focus:border-teal-500 focus:ring-teal-500 text-lg"
                    required
                  />
                </div>

                {/* Slug */}
                <div>
                  <label className="block text-white font-medium mb-2">Slug</label>
                  <input
                    type="text"
                    name="slug"
                    value={formData.slug}
                    onChange={handleInputChange}
                    placeholder="url-friendly-slug"
                    className="w-full bg-gray-800 text-white border border-gray-700 rounded px-4 py-2 focus:border-teal-500 focus:ring-teal-500"
                  />
                  <p className="text-gray-400 text-sm mt-1">
                    URL: /blog/{formData.slug || 'your-slug-here'}
                  </p>
                </div>

                {/* Excerpt */}
                <div>
                  <label className="block text-white font-medium mb-2">Excerpt *</label>
                  <textarea
                    name="excerpt"
                    value={formData.excerpt}
                    onChange={handleInputChange}
                    placeholder="Brief description of the blog post..."
                    rows={3}
                    className="w-full bg-gray-800 text-white border border-gray-700 rounded px-4 py-3 focus:border-teal-500 focus:ring-teal-500"
                    required
                  />
                </div>

                {/* Content */}
                <div>
                  <label className="block text-white font-medium mb-2">Content *</label>
                  <textarea
                    name="content"
                    value={formData.content}
                    onChange={handleInputChange}
                    placeholder="Write your blog post content here... (Supports Markdown)"
                    rows={20}
                    className="w-full bg-gray-800 text-white border border-gray-700 rounded px-4 py-3 focus:border-teal-500 focus:ring-teal-500 font-mono"
                    required
                  />
                  <p className="text-gray-400 text-sm mt-1">
                    Estimated reading time: {readingTime} minute{readingTime !== 1 ? 's' : ''}
                  </p>
                </div>

                {/* Featured Image */}
                <div>
                  <label className="block text-white font-medium mb-2">Featured Image URL</label>
                  <input
                    type="url"
                    name="featuredImage"
                    value={formData.featuredImage}
                    onChange={handleInputChange}
                    placeholder="https://example.com/image.jpg"
                    className="w-full bg-gray-800 text-white border border-gray-700 rounded px-4 py-2 focus:border-teal-500 focus:ring-teal-500"
                  />
                  {formData.featuredImage && (
                    <div className="mt-3">
                      <img
                        src={formData.featuredImage}
                        alt="Featured image preview"
                        className="w-full max-w-md h-48 object-cover rounded"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            ) : (
              /* Preview Mode */
              <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
                <h1 className="text-4xl font-bold text-white mb-4">{formData.title || 'Untitled Post'}</h1>
                <p className="text-xl text-gray-300 mb-6">{formData.excerpt}</p>
                
                {formData.featuredImage && (
                  <img
                    src={formData.featuredImage}
                    alt="Featured"
                    className="w-full h-64 object-cover rounded mb-6"
                  />
                )}
                
                <div className="prose prose-invert max-w-none">
                  <div 
                    className="text-gray-300 leading-relaxed whitespace-pre-wrap"
                  >
                    {formData.content || 'No content yet...'}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Publish Actions */}
            <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6">
              <h3 className="text-white font-semibold mb-4">Publish</h3>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isPublished"
                    name="isPublished"
                    checked={formData.isPublished}
                    onChange={handleInputChange}
                    className="rounded"
                  />
                  <label htmlFor="isPublished" className="ml-2 text-white">
                    Publish immediately
                  </label>
                </div>

                <div className="flex flex-col gap-2">
                  <button
                    onClick={() => handleSave(false)}
                    disabled={isSaving}
                    className="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded transition-colors disabled:opacity-50"
                  >
                    {isSaving ? 'Saving...' : 'Save Draft'}
                  </button>
                  <button
                    onClick={() => handleSave(true)}
                    disabled={isSaving || !formData.title || !formData.content}
                    className="w-full bg-teal-600 hover:bg-teal-500 text-white py-2 px-4 rounded transition-colors disabled:opacity-50"
                  >
                    {isSaving ? 'Publishing...' : 'Publish'}
                  </button>
                </div>
              </div>
            </div>

            {/* Category */}
            <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6">
              <h3 className="text-white font-semibold mb-4">Category</h3>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500"
              >
                <option value="">Select category...</option>
                {mockBlogCategories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Tags */}
            <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6">
              <h3 className="text-white font-semibold mb-4">Tags</h3>
              <div className="space-y-2">
                {mockBlogTags.map(tag => (
                  <label key={tag.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.tags.includes(tag.slug)}
                      onChange={() => handleTagToggle(tag.slug)}
                      className="rounded"
                    />
                    <span className="ml-2 text-white">{tag.name}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* SEO */}
            <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6">
              <h3 className="text-white font-semibold mb-4">SEO</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-white font-medium mb-2">Meta Title</label>
                  <input
                    type="text"
                    name="metaTitle"
                    value={formData.metaTitle}
                    onChange={handleInputChange}
                    placeholder="SEO title..."
                    className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500 text-sm"
                  />
                </div>
                
                <div>
                  <label className="block text-white font-medium mb-2">Meta Description</label>
                  <textarea
                    name="metaDescription"
                    value={formData.metaDescription}
                    onChange={handleInputChange}
                    placeholder="SEO description..."
                    rows={3}
                    className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500 text-sm"
                  />
                </div>
                
                <div>
                  <label className="block text-white font-medium mb-2">Keywords</label>
                  <input
                    type="text"
                    name="keywords"
                    value={formData.keywords}
                    onChange={handleInputChange}
                    placeholder="keyword1, keyword2, keyword3"
                    className="w-full bg-gray-800 text-white border border-gray-700 rounded px-3 py-2 focus:border-teal-500 focus:ring-teal-500 text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
