import "@/app/globals.css";
import React from "react";
import ClientBody from "./ClientBody";
import { AltoraLogo } from "../components/AltoraLogo";
import Link from "next/link";

const POLYSANS_FONT_URLS = [
  {
    url: "https://ext.same-assets.com/4123950039/3713092101.ttf",
    weight: "400",
    style: "normal",
  },
  {
    url: "https://ext.same-assets.com/4123950039/1287537253.ttf",
    weight: "500",
    style: "normal",
  },
  {
    url: "https://ext.same-assets.com/4123950039/523861748.ttf",
    weight: "700",
    style: "normal",
  },
];

export const metadata = {
  title: "Altora – AI Production Design Studio",
  description:
    "Professional design tools for TV series, movies, and entertainment productions. 110+ styles, authentic materials, and cinematic environments. Free to try.",
};

const NAV_LINKS = [
  { label: "Pricing", href: "/pricing" },
  { label: "Glossary", href: "/interior-design-glossary" },
  { label: "Advice", href: "/advice" },
  { label: "Podcast", href: "/podcast" },
];

function Header() {
  return (
    <header className="sticky top-0 z-30 w-full bg-gray-950/80 backdrop-blur-lg border-b border-gray-800/50">
      <nav className="flex items-center justify-between px-6 py-4 max-w-7xl mx-auto">
        <Link href="/" className="flex items-center gap-2">
          <AltoraLogo size="md" />
        </Link>
        <div className="flex gap-4 items-center">
          <Link
            href="/blog"
            className="text-gray-400 hover:text-white transition-colors text-sm font-medium"
          >
            Blog
          </Link>
          <Link
            href="#pricing"
            className="text-gray-400 hover:text-white transition-colors text-sm font-medium"
          >
            Pricing
          </Link>
          <Link
            href="/profile"
            className="text-gray-400 hover:text-white transition-colors text-sm font-medium"
          >
            Profile
          </Link>
          <Link
            href="/signin"
            className="text-gray-400 hover:text-white transition-colors text-sm font-medium"
          >
            Log in
          </Link>
          <Link
            href="/design/new"
            className="px-4 py-2 rounded bg-[#2dd4bf] text-black font-semibold hover:bg-[#14b8a6] transition-all text-sm"
          >
            Design your interior
          </Link>
        </div>
      </nav>
    </header>
  );
}

function Footer() {
  return (
    <footer className="bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 border-t border-gray-800">
      <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Column */}
          <div>
            <AltoraLogo size="md" />
            <p className="text-gray-400 mt-4 text-sm">
              Transform your space with AI-powered interior design. Create
              stunning rooms in minutes.
            </p>
            <p className="text-gray-400 text-sm mt-4">
              © 2024 altora.design. All rights reserved.
            </p>
          </div>

          {/* Contact Column */}
          <div>
            <h3 className="text-white font-semibold mb-4">Contact</h3>
            <div className="space-y-3">
              <p className="text-gray-400 text-sm">
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-400 hover:underline"
                >
                  <EMAIL>
                </a>
              </p>

              <div className="flex items-center gap-3 mt-4">
                <a
                  href="https://x.com/altora_design"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                  title="Follow us on X (Twitter)"
                >
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                </a>
                <a
                  href="https://www.instagram.com/altora_designs/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                  title="Follow us on Instagram"
                >
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                  </svg>
                </a>
                <a
                  href="https://www.facebook.com/profile.php?id=61578904993221"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                  title="Follow us on Facebook"
                >
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                  </svg>
                </a>
              </div>
            </div>
          </div>

          {/* Pages Column */}
          <div>
            <h3 className="text-white font-semibold mb-4">Pages</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="/blog"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Blog
                </a>
              </li>
              <li>
                <a
                  href="/profile"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Profile
                </a>
              </li>
              <li>
                <a
                  href="/signup"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Sign Up
                </a>
              </li>
              <li>
                <a
                  href="/signin"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Sign In
                </a>
              </li>
              <li>
                <a
                  href="/terms-of-service"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Terms of Service
                </a>
              </li>
              <li>
                <a
                  href="/privacy-policy"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Privacy Policy
                </a>
              </li>
              {/* <li><a href="/faq" className="text-gray-400 hover:text-white transition-colors text-sm">FAQ</a></li> */}
            </ul>
          </div>

          {/* Links Column */}
          <div>
            <h3 className="text-white font-semibold mb-4">Links</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="#pricing"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Pricing
                </a>
              </li>
              <li>
                <a
                  href="#features"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Features
                </a>
              </li>
              <li>
                <a
                  href="#how-it-works"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  How It Works
                </a>
              </li>
              {/* <li><a href="#testimonials" className="text-gray-400 hover:text-white transition-colors text-sm">Testimonials</a></li> */}
              <li>
                <a
                  href="#faq"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  FAQ
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head />
      <body className="bg-gray-950 text-white font-sans min-h-screen flex flex-col">
        <Header />
        <ClientBody>{children}</ClientBody>
        <Footer />
      </body>
    </html>
  );
}
