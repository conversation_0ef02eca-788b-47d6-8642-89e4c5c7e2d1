"use client";
import React, { useState, useEffect } from "react";
import HeroSection from "../components/HeroSection";

// FAQ data
const FAQS = [
  {
    q: "What is altora.design?",
    a: "altora.design is an AI-powered interior design platform that helps homeowners, renters, designers, and professionals create stunning room designs in minutes. Simply upload a photo of your space and let our AI transform it with over 110+ professional design styles."
  },
  {
    q: "Who can use altora.design?",
    a: "altora.design is designed for everyone - from homeowners wanting to redesign their living space to professional interior designers, real estate agents, and architects. Whether you're planning a renovation, staging a home, or just exploring design ideas, altora.design makes professional design accessible to all."
  },
  {
    q: "How does the AI design process work?",
    a: "Our advanced AI analyzes your room photo, understands the space layout, furniture placement, and lighting, then applies your chosen design style while maintaining the room's structure and proportions. The process typically takes 30-60 seconds to generate multiple high-quality design variations."
  },
  {
    q: "What types of rooms and spaces can I design?",
    a: "You can design any interior space including living rooms, bedrooms, kitchens, bathrooms, home offices, dining rooms, nurseries, basements, garages, laundry rooms, attics, walk-in closets, and more. Our AI works with both residential and commercial spaces of any size."
  },
  {
    q: "How many design styles are available?",
    a: "We offer 110+ professionally curated design styles including Modern, Scandinavian, Industrial, Bohemian, Traditional, Minimalist, Mid-Century Modern, Farmhouse, Art Deco, and many more. Each style includes detailed descriptions and is crafted by professional interior designers."
  },
  {
    q: "Can I customize colors and materials?",
    a: "Yes! altora.design offers extensive customization options including 12+ color palettes (from Soft Neutrals to Golden Hour), 20+ material options (wood, metal, stone, glass, fabrics), and the ability to mix and match different elements to create your perfect design."
  },
  {
    q: "What image quality do I need for best results?",
    a: "For optimal results, use high-resolution images (at least 1024x1024 pixels) with good lighting and clear visibility of the room. We support JPG, PNG, and WebP formats. Well-lit photos with minimal clutter produce the most accurate and impressive transformations."
  },
  {
    q: "Is altora.design suitable for professional use?",
    a: "Absolutely! Many interior designers use altora.design to quickly generate design concepts for clients, real estate agents use it for virtual staging, and architects use it for presentation materials. Our Pro plans include commercial usage rights and high-resolution outputs perfect for professional presentations."
  },
  {
    q: "How secure is my data and images?",
    a: "We take privacy seriously. All images are processed using enterprise-grade security measures, encrypted during transmission and storage, and are never shared with third parties. You maintain full ownership of your images and can delete them at any time."
  },
  {
    q: "Can I try altora.design for free?",
    a: "Yes! Our free plan includes 3 design generations per month, access to 5 popular design styles, and standard resolution outputs. It's perfect for exploring the platform and small personal projects before upgrading to a paid plan."
  },
  {
    q: "What's the difference between plans?",
    a: "Our Free plan offers basic features, Pro plan includes unlimited generations and all 110+ styles, and Enterprise plan adds commercial rights, priority support, and team collaboration features. All plans include our core AI technology and regular style updates."
  },
  {
    q: "Can I use altora.design designs for renovation planning?",
    a: "Yes! Many users use altora.design to visualize renovation ideas, experiment with different styles before committing to purchases, and share design concepts with contractors or family members. It's a cost-effective way to explore possibilities before making expensive design decisions."
  }
];
import { STYLES } from "../components/StyleSelector";

// Sample data for the new sections
const ROOM_TYPES = [
  {
    title: "Living Room",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M3 9v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V9H3zm2 2h4v4H5v-4zm6 0h8v4h-8v-4zM7 7h10c.55 0 1-.45 1-1s-.45-1-1-1H7c-.55 0-1 .45-1 1s.45 1 1 1z"/>
      </svg>
    )
  },
  {
    title: "Bedroom",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M21 10.78V8c0-1.65-1.35-3-3-3h-4c-.77 0-1.47.3-2 .78-.53-.48-1.23-.78-2-.78H6c-1.65 0-3 1.35-3 3v2.78c-.61.55-1 1.34-1 2.22v6h2v-2h16v2h2v-6c0-.88-.39-1.67-1-2.22zM5 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2H5V8zm8 0c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2h-6V8zM3 13h18v2H3v-2z"/>
      </svg>
    )
  },
  {
    title: "Kitchen",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M18 2.01L6 2c-1.11 0-2 .89-2 2v16c0 1.11.89 2 2 2h12c1.11 0 2-.89 2-2V4c0-1.11-.89-1.99-2-1.99zM18 20H6V4h12v16zM8 6h8v2H8V6zm0 3h8v2H8V9zm0 3h5v2H8v-2z"/>
        <circle cx="8" cy="17" r="1"/>
        <circle cx="12" cy="17" r="1"/>
        <circle cx="16" cy="17" r="1"/>
      </svg>
    )
  },
  {
    title: "Bathroom",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M7 7h10v1H7V7zm0 3h10v1H7v-1zm12-1.5c0-.83-.67-1.5-1.5-1.5S16 7.67 16 8.5V9H8v-.5C8 7.67 7.33 7 6.5 7S5 7.67 5 8.5V16c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4V8.5zM7 16v-5h10v5c0 1.1-.9 2-2 2H9c-1.1 0-2-.9-2-2z"/>
        <circle cx="6.5" cy="5" r="1.5"/>
        <circle cx="17.5" cy="5" r="1.5"/>
      </svg>
    )
  },
  {
    title: "Dining Room",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M22 7h-3V6c0-.55-.45-1-1-1s-1 .45-1 1v1h-4V6c0-.55-.45-1-1-1s-1 .45-1 1v1H7V6c0-.55-.45-1-1-1s-1 .45-1 1v1H2c-.55 0-1 .45-1 1s.45 1 1 1h1v9c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V9h1c.55 0 1-.45 1-1s-.45-1-1-1zM5 18V9h14v9H5z"/>
        <circle cx="8" cy="12" r="1"/>
        <circle cx="12" cy="12" r="1"/>
        <circle cx="16" cy="12" r="1"/>
      </svg>
    )
  },
  {
    title: "Home Office",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M21 2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h7l-2 3v1h8v-1l-2-3h7c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 12H3V4h18v10z"/>
        <path d="M5 6h14v6H5V6z"/>
        <circle cx="6" cy="15" r="1"/>
        <circle cx="18" cy="15" r="1"/>
      </svg>
    )
  },
  {
    title: "Nursery",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        <circle cx="9" cy="9" r="1"/>
        <circle cx="15" cy="9" r="1"/>
        <path d="M12 17.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"/>
      </svg>
    )
  },
  {
    title: "Basement",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 3L2 12h3v8h14v-8h3L12 3zm0 2.84L18.16 12H17v6H7v-6H5.84L12 5.84z"/>
        <path d="M8 14h8v1H8v-1zm0 2h8v1H8v-1zm0 2h6v1H8v-1z"/>
      </svg>
    )
  },
  {
    title: "Garage",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M20 8h-3V6c0-1.1-.9-2-2-2H9c-1.1 0-2 .9-2 2v2H4c-1.1 0-2 .9-2 2v10h20V10c0-1.1-.9-2-2-2zM9 6h6v2H9V6zM4 18v-6h16v6H4z"/>
        <rect x="6" y="12" width="3" height="4"/>
        <rect x="15" y="12" width="3" height="4"/>
        <circle cx="7.5" cy="15" r="1"/>
        <circle cx="16.5" cy="15" r="1"/>
      </svg>
    )
  },
  {
    title: "Laundry Room",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M18 2.01L6 2c-1.11 0-2 .89-2 2v16c0 1.11.89 2 2 2h12c1.11 0 2-.89 2-2V4c0-1.11-.89-1.99-2-1.99zM18 20H6V4h12v16z"/>
        <circle cx="12" cy="12" r="6"/>
        <circle cx="12" cy="12" r="3"/>
        <circle cx="8" cy="6" r="1"/>
        <circle cx="16" cy="6" r="1"/>
      </svg>
    )
  },
  {
    title: "Attic",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 3L2 12h3v8h6v-6h2v6h6v-8h3L12 3zm0 2.84L18.16 12H17v6h-2v-6H9v6H7v-6H5.84L12 5.84z"/>
        <path d="M10 8h4v1h-4V8zm-1 2h6v1H9v-1z"/>
        <circle cx="12" cy="6" r="1"/>
      </svg>
    )
  },
  {
    title: "Walk-in Closet",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M6 2c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2H6zm0 2h5v16H6V4zm7 0h5v16h-5V4z"/>
        <path d="M7 6h3v1H7V6zm0 2h3v1H7V8zm0 2h3v1H7v-1zm7 0h3v1h-3v-1zm0-2h3v1h-3V8zm0-2h3v1h-3V6z"/>
        <circle cx="10" cy="12" r="0.5"/>
        <circle cx="14" cy="12" r="0.5"/>
      </svg>
    )
  }
];

const COLOR_PALETTES = [
  {
    name: "Soft Neutrals",
    colors: ["#FFFFFF", "#F5F5F5", "#E8E8E8", "#C0C0C0"]
  },
  {
    name: "Coastal Calm",
    colors: ["#E6F3FF", "#B3D9FF", "#7AC3FF", "#4A90E2"]
  },
  {
    name: "Nordic Lights",
    colors: ["#F8F9FA", "#E9ECEF", "#ADB5BD", "#6C757D"]
  },
  {
    name: "Forest Retreat",
    colors: ["#D4E6B7", "#A8CC8C", "#7FB069", "#4F7942"]
  },
  {
    name: "Crisp Winter",
    colors: ["#F0F8FF", "#E6F3FF", "#CCE7FF", "#99D6FF"]
  },
  {
    name: "Sea Breeze",
    colors: ["#E0F7FA", "#B2EBF2", "#4DD0E1", "#00ACC1"]
  },
  {
    name: "Fresh Mint",
    colors: ["#00FF7F", "#98FB98", "#90EE90", "#F0FFF0"]
  },
  {
    name: "Chocolate Brown",
    colors: ["#3C1810", "#654321", "#8B4513", "#A0522D"]
  },
  {
    name: "Peacock Blue",
    colors: ["#005F69", "#008B8B", "#20B2AA", "#48D1CC"]
  },
  {
    name: "Coral Reef",
    colors: ["#FF5722", "#FF7043", "#FF8A65", "#FFAB91"]
  },
  {
    name: "Stormy Sky",
    colors: ["#2F4F4F", "#708090", "#778899", "#B0C4DE"]
  },
  {
    name: "Golden Hour",
    colors: ["#B8860B", "#DAA520", "#FFD700", "#FFFF00"]
  }
];

const MATERIALS = [
  { name: "Walnut", category: "Wood" },
  { name: "Steel", category: "Metal" },
  { name: "Rosewood", category: "Wood" },
  { name: "Terrazzo", category: "Stone" },
  { name: "Basalt", category: "Stone" },
  { name: "Colored", category: "Glass" },
  { name: "Chrome", category: "Metal" },
  { name: "Mirrored", category: "Glass" },
  { name: "Wool", category: "Fabrics" },
  { name: "Tempered", category: "Glass" },
  { name: "Stainless steel", category: "Metal" },
  { name: "Clear", category: "Glass" },
  { name: "Maple", category: "Wood" },
  { name: "Mahogany", category: "Wood" },
  { name: "Silver", category: "Metal" },
  { name: "Granite", category: "Stone" },
  { name: "Marble", category: "Stone" },
  { name: "Copper", category: "Metal" },
  { name: "Linen", category: "Fabrics" },
  { name: "Ceramic", category: "Stone" }
];

export default function HomePage() {
  const [isYearly, setIsYearly] = useState(false);
  const [animatingMaterials, setAnimatingMaterials] = useState<Set<string>>(new Set());

  // Predictable animation effect for materials (no random to avoid hydration issues)
  useEffect(() => {
    let animationIndex = 0;
    const interval = setInterval(() => {
      // Use predictable pattern instead of random
      const materialNames = MATERIALS.map(m => m.name);
      const selectedMaterials = new Set<string>();

      // Animate 1-2 materials in a predictable pattern
      const numToAnimate = (animationIndex % 3) === 0 ? 1 : 1;
      for (let i = 0; i < numToAnimate; i++) {
        const index = (animationIndex + i) % materialNames.length;
        selectedMaterials.add(materialNames[index]);
      }

      setAnimatingMaterials(selectedMaterials);
      animationIndex = (animationIndex + 1) % materialNames.length;

      // Stop animation after 800ms
      setTimeout(() => {
        setAnimatingMaterials(new Set());
      }, 200);
    }, 500); // Every 0.5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      {/* Hero Section */}
      <HeroSection
        title="A home you can’t wait to come back to"
        subtitle="Transform any space with AI-powered interior design. Whether you're a homeowner, renter, designer, or professional - create stunning rooms in minutes with our easy-to-use AI software."
        primaryButtonText="Get started for free"
        primaryButtonHref="/signin"
        secondaryButtonText="See pricing"
        secondaryButtonHref="#pricing"
      />

      {/* Inspiration Sections */}
      <section className="w-full bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
        <div className="max-w-7xl mx-auto px-6">
          {/* Rooms and Materials Section - Side by Side */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* Rooms Section */}
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Rooms</h2>
              </div>
              <p className="text-gray-400 mb-6">No matter what type of room you're designing, we've got you covered.</p>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {ROOM_TYPES.map((room) => (
                  <div key={room.title} className="group cursor-pointer">
                    <div className="flex flex-col items-center p-4 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors duration-300 border border-gray-700 hover:border-gray-600">
                      <div className="text-gray-300 mb-3">{room.icon}</div>
                      <h3 className="text-white font-medium text-center text-sm">{room.title}</h3>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Materials Section */}
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Materials</h2>
              </div>
              <p className="text-gray-400 mb-6">Try out dozens of materials to see how they would look in your interior.</p>
              <div className="grid grid-cols-3 md:grid-cols-5 gap-3">
                {MATERIALS.map((material) => {
                  const isAnimating = animatingMaterials.has(material.name);
                  return (
                    <div key={material.name} className="group cursor-pointer">
                      <div className={`flex flex-col items-center p-4 rounded-lg border transition-all duration-150 ${
                        isAnimating
                          ? 'bg-teal-600/20 border-teal-400 scale-105 shadow-lg shadow-teal-500/25'
                          : 'bg-gray-800 border-gray-700'
                      } group-hover:!bg-gray-700 group-hover:!border-gray-600 group-hover:!scale-105 group-hover:!shadow-lg group-hover:!shadow-gray-500/20`}>
                        <h3 className={`font-medium text-center text-sm transition-colors duration-150 ${
                          isAnimating ? 'text-teal-300' : 'text-white'
                        } group-hover:!text-white`}>
                          {material.name}
                        </h3>
                        <p className={`text-xs text-center mt-1 transition-colors duration-150 ${
                          isAnimating ? 'text-teal-400' : 'text-gray-400'
                        } group-hover:!text-gray-300`}>
                          {material.category}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Colors Section */}
          <div className="mb-16">
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Colors</h2>
              </div>
              <p className="text-gray-400 mb-6">Choose your own colors, or have AI generate a color palette that matches your style and room.</p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                {COLOR_PALETTES.map((palette) => (
                  <div key={palette.name} className="group cursor-pointer">
                    <div className="flex -space-x-1 mb-3 justify-center">
                      {palette.colors.map((color, colorIdx) => (
                        <div
                          key={colorIdx}
                          className="w-8 h-8 rounded-full hover:scale-110 transition-transform duration-300 cursor-pointer hover:z-10 relative border-[1px] border-gray-700"
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                    <h3 className="text-white font-medium text-center text-sm">{palette.name}</h3>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="w-full bg-gray-950 py-16">
  <div className="max-w-7xl mx-auto px-6">
    <div className="text-center mb-16">
      <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
        Designed for Every Vision
      </h2>
      <p className="text-gray-400 text-lg max-w-3xl mx-auto">
        From personal home makeovers to professional design projects, Altora empowers
        anyone to reimagine spaces with ease, creativity, and precision.
      </p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      {/* Homeowners */}
      <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
        <div className="w-16 h-16 bg-teal-600 rounded-lg flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
          </svg>
        </div>
        <h3 className="text-white font-semibold text-xl mb-3">Homeowners</h3>
        <p className="text-gray-400 text-sm mb-4">
          Turn your dream home into reality without costly guesswork. Altora’s AI
          helps you visualize every idea before you commit.
        </p>
        <p className="text-gray-400 text-sm">
          Experiment with colors, layouts, and furniture to see what truly fits your lifestyle.
        </p>
      </div>

      {/* Interior Designers */}
      <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
        <div className="w-16 h-16 bg-teal-600 rounded-lg flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        </div>
        <h3 className="text-white font-semibold text-xl mb-3">Interior Designers</h3>
        <p className="text-gray-400 text-sm mb-4">
          Bring your concepts to life in minutes. Impress clients with multiple
          design options and secure approvals faster.
        </p>
        <p className="text-gray-400 text-sm">
          Blend your creativity with Altora’s AI efficiency for a winning combination.
        </p>
      </div>

      {/* Real Estate Agents */}
      <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
        <div className="w-16 h-16 bg-teal-600 rounded-lg flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
          </svg>
        </div>
        <h3 className="text-white font-semibold text-xl mb-3">Real Estate Agents</h3>
        <p className="text-gray-400 text-sm mb-4">
          Show potential buyers the hidden potential of any property with beautiful,
          AI-generated interiors.
        </p>
        <p className="text-gray-400 text-sm">
          Make your listings stand out and close deals faster with captivating visuals.
        </p>
      </div>

      {/* Architects */}
      <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
        <div className="w-16 h-16 bg-teal-600 rounded-lg flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
          </svg>
        </div>
        <h3 className="text-white font-semibold text-xl mb-3">Architects</h3>
        <p className="text-gray-400 text-sm mb-4">
          Streamline your workflow and offer clients a tangible glimpse of their future space.
        </p>
        <p className="text-gray-400 text-sm">
          Use Altora to quickly explore interior possibilities without complex rendering tools.
        </p>
      </div>
    </div>
  </div>
</section>

      {/* How It Works Section */}
      <section className="w-full bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              How It Works
            </h2>
            <p className="text-gray-400 text-lg max-w-3xl mx-auto">
              Transform your space in just 3 simple steps. Our AI technology makes professional interior design accessible to everyone.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-20 h-20 bg-gradient-to-br from-teal-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  1
                </div>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Upload Your Photo</h3>
              <p className="text-gray-400">
                Take a photo of your room or upload an existing image. Our AI works with any space - from empty rooms to fully furnished areas.
              </p>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                  </svg>
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  2
                </div>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Choose Your Style</h3>
              <p className="text-gray-400">
                Select from 110+ professional design styles, customize colors and materials, and set your preferences for the perfect look.
              </p>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  3
                </div>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Get AI Results</h3>
              <p className="text-gray-400">
                Watch as our AI transforms your space in 30-60 seconds. Download high-resolution images and share your new design vision.
              </p>
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-12">
            <a
              href="/start"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white font-semibold rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Try It Free Now
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </a>
          </div>
        </div>
      </section>

      {/* Styles Showcase Section */}
      <section className="w-full bg-gray-950 py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Choose from 110+ interior design styles
            </h2>
            <p className="text-gray-400 text-lg max-w-3xl mx-auto">
              From Modern Minimalist to Bohemian Chic, our AI understands every design aesthetic. No complex prompts needed - just select your style and watch your space transform instantly.
            </p>
            <p className="text-teal-400 font-medium mt-4">
              All styles included in Pro plans! Experiment with unlimited combinations.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {STYLES.slice(0, 18).map((style) => (
              <div key={style.id} className="group cursor-pointer bg-gray-900 rounded-xl overflow-hidden border border-gray-800 hover:border-teal-500 transition-all duration-300">
                <div className="relative aspect-video overflow-hidden">
                  <img
                    src={style.image}
                    alt={style.label}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300" />
                </div>
                <div className="p-4">
                  <h3 className="text-white font-semibold text-lg mb-2">{style.label}</h3>
                  <p className="text-gray-400 text-sm">{style.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <a
              href="/design/new"
              className="inline-flex items-center px-8 py-4 bg-teal-600 text-white font-semibold rounded-lg hover:bg-teal-500 transition-colors duration-300"
            >
              Explore All Styles
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full bg-gray-950 py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Powerful Features for Every Designer
            </h2>
            <p className="text-gray-400 text-lg max-w-3xl mx-auto">
              Professional-grade tools and AI technology that make interior design accessible, fast, and incredibly detailed.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-teal-400 to-blue-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Lightning Fast AI</h3>
              <p className="text-gray-400">
                Generate stunning room designs in 30-60 seconds. Our advanced AI processes your images instantly for immediate results.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">110+ Design Styles</h3>
              <p className="text-gray-400">
                From Modern Minimalist to Bohemian Chic, explore every design aesthetic with professionally curated style options.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Custom Colors & Materials</h3>
              <p className="text-gray-400">
                Personalize every detail with 12+ color palettes and 20+ material options. Mix and match to create your perfect space.
              </p>
            </div>

            {/* Feature 4 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-teal-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Enterprise Security</h3>
              <p className="text-gray-400">
                Your images are encrypted and secure. We never share your data and you maintain full ownership of all uploads.
              </p>
            </div>

            {/* Feature 5 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">High-Resolution Output</h3>
              <p className="text-gray-400">
                Download professional-quality images perfect for presentations, social media, or printing. Crystal clear results every time.
              </p>
            </div>

            {/* Feature 6 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800 hover:border-teal-500 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-400 to-blue-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Professional Use</h3>
              <p className="text-gray-400">
                Commercial licensing included in Pro plans. Perfect for designers, real estate agents, and architects serving clients.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="w-full bg-gradient-to-br from-gray-900 via-gray-950 to-black py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Simple, transparent pricing
            </h2>
            <p className="text-gray-400 text-lg">
              Choose the plan that's right for you
            </p>
          </div>

          {/* Pricing Toggle */}
          <div className="flex justify-center mb-12">
            <div className="bg-gray-800 p-1 rounded-lg">
              <button
                onClick={() => setIsYearly(false)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  !isYearly ? 'bg-teal-600 text-white' : 'text-gray-400 hover:text-white'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setIsYearly(true)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  isYearly ? 'bg-teal-600 text-white' : 'text-gray-400 hover:text-white'
                }`}
              >
                Yearly <span className="text-teal-400 text-sm">(33% off)</span>
              </button>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Personal Plan */}
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <h3 className="text-xl font-bold text-white mb-2">Personal</h3>
              <p className="text-gray-400 text-sm mb-6">
                Discover your inner designer and create your dream home with our cost-effective, user-friendly software.
              </p>
              <div className="mb-6">
                {isYearly ? (
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-2xl text-gray-400 line-through">$29</span>
                      <span className="text-4xl font-bold text-white">$19</span>
                      <span className="text-gray-400">/month</span>
                    </div>
                    <p className="text-sm text-gray-400 mt-1">Billed $228 yearly</p>
                  </div>
                ) : (
                  <div>
                    <span className="text-4xl font-bold text-white">$29</span>
                    <span className="text-gray-400">/month</span>
                  </div>
                )}
              </div>
              <button className="w-full bg-gray-800 text-white py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors mb-6">
                Start designing
              </button>
              <ul className="space-y-3 text-sm">
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  250 images per month
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Personal-use only
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Small watermark
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Normal resolution
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  1 user
                </li>
              </ul>
              <p className="text-gray-500 text-xs mt-4">Cancel anytime</p>
            </div>

            {/* Pro Plan */}
            <div className="bg-gray-900 rounded-2xl p-8 border-2 border-teal-500 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Most popular
                </span>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Pro</h3>
              <p className="text-gray-400 text-sm mb-6">
                Brainstorm ideas quickly, impress clients with stunning visuals, and close deals faster using our professional tools.
              </p>
              <div className="mb-6">
                {isYearly ? (
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-2xl text-gray-400 line-through">$99</span>
                      <span className="text-4xl font-bold text-white">$69</span>
                      <span className="text-gray-400">/month</span>
                    </div>
                    <p className="text-sm text-gray-400 mt-1">Billed $828 yearly</p>
                  </div>
                ) : (
                  <div>
                    <span className="text-4xl font-bold text-white">$99</span>
                    <span className="text-gray-400">/month</span>
                  </div>
                )}
              </div>
              <button className="w-full bg-teal-600 text-white py-3 rounded-lg font-medium hover:bg-teal-500 transition-colors mb-6">
                Start designing
              </button>
              <ul className="space-y-3 text-sm">
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  1,000 images per month
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Commercial license
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  No watermark
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Highest resolution
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  1 user
                </li>
              </ul>
              <p className="text-gray-500 text-xs mt-4">Cancel anytime</p>
            </div>

            {/* Team Plan */}
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <h3 className="text-xl font-bold text-white mb-2">Team</h3>
              <p className="text-gray-400 text-sm mb-6">
                Empower your firm with cutting-edge AI technology to enhance efficiency and stay competitive in the market.
              </p>
              <div className="mb-6">
                {isYearly ? (
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-2xl text-gray-400 line-through">$299</span>
                      <span className="text-4xl font-bold text-white">$199</span>
                      <span className="text-gray-400">/month</span>
                    </div>
                    <p className="text-sm text-gray-400 mt-1">Billed $2,388 yearly</p>
                  </div>
                ) : (
                  <div>
                    <span className="text-4xl font-bold text-white">$299</span>
                    <span className="text-gray-400">/month</span>
                  </div>
                )}
              </div>
              <button className="w-full bg-gray-800 text-white py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors mb-6">
                Start designing
              </button>
              <ul className="space-y-3 text-sm">
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  5,000 images per month
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Commercial license
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  No watermark
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Highest resolution
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Up to 5 users
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Train your own style
                </li>
              </ul>
              <p className="text-gray-500 text-xs mt-4">Cancel anytime</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="w-full bg-gray-950 py-16" id="faq">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Frequently Asked questions.
            </h2>
            {/* <p className="text-gray-400 text-lg">
              Everything you need to know about altora.design
            </p> */}
          </div>

          <div className="space-y-4">
            {FAQS.map((faq, i) => (
              <details key={i} className="group bg-gray-900 rounded-lg border border-gray-800">
                <summary className="flex justify-between items-center cursor-pointer p-6 text-white font-medium text-lg hover:text-teal-400 transition-colors">
                  {faq.q}
                  <svg className="w-5 h-5 transform group-open:rotate-180 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </summary>
                <div className="px-6 pb-6 text-gray-400 whitespace-pre-line">
                  {faq.a}
                </div>
              </details>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}
