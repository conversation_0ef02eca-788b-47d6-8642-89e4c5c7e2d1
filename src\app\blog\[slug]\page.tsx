"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { mockBlogPosts, formatDate, getRelativeTime } from '@/lib/blog';

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [likes, setLikes] = useState(0);

  // Find the blog post by slug
  const post = mockBlogPosts.find(p => p.slug === params.slug);

  if (!post) {
    notFound();
  }

  // Initialize likes from post data
  React.useEffect(() => {
    setLikes(post.likes);
  }, [post.likes]);

  const handleLike = () => {
    if (isLiked) {
      setLikes(prev => prev - 1);
    } else {
      setLikes(prev => prev + 1);
    }
    setIsLiked(!isLiked);
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const title = post.title;
    
    let shareUrl = '';
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
      case 'copy':
        navigator.clipboard.writeText(url);
        alert('Link copied to clipboard!');
        return;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  // Related posts (simple implementation)
  const relatedPosts = mockBlogPosts
    .filter(p => p.id !== post.id && p.tags.some(tag => post.tags.includes(tag)))
    .slice(0, 3);

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950">
      {/* Hero Section */}
      <div className="relative">
        {post.featuredImage && (
          <div className="relative h-96 overflow-hidden">
            <img
              src={post.featuredImage}
              alt={post.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/50" />
          </div>
        )}
        
        <div className="relative max-w-4xl mx-auto px-4 py-12">
          {/* Breadcrumbs */}
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 text-sm text-gray-400">
              <li><Link href="/" className="hover:text-white">Home</Link></li>
              <li><span className="mx-2">•</span></li>
              <li><Link href="/blog" className="hover:text-white">Blog</Link></li>
              <li><span className="mx-2">•</span></li>
              <li className="text-white">{post.title}</li>
            </ol>
          </nav>

          {/* Article Header */}
          <header className="text-center mb-8">
            <div className="mb-4">
              <span className="bg-teal-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                {post.category}
              </span>
            </div>
            
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {post.title}
            </h1>
            
            <p className="text-xl text-gray-300 mb-6 max-w-3xl mx-auto">
              {post.excerpt}
            </p>

            {/* Author and Meta Info */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-gray-400">
              <div className="flex items-center gap-3">
                <img
                  src={post.author.avatar}
                  alt={post.author.name}
                  className="w-12 h-12 rounded-full border-2 border-teal-500"
                />
                <div className="text-left">
                  <p className="text-white font-medium">{post.author.name}</p>
                  <p className="text-sm">{post.author.bio}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4 text-sm">
                <span>{formatDate(post.publishedAt)}</span>
                <span>•</span>
                <span>{post.readingTime} min read</span>
                <span>•</span>
                <span>{post.views} views</span>
              </div>
            </div>
          </header>
        </div>
      </div>

      {/* Article Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <article className="lg:col-span-3">
            <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
              {/* Content */}
              <div className="prose prose-invert prose-teal max-w-none">
                <div 
                  className="text-gray-300 leading-relaxed"
                  dangerouslySetInnerHTML={{ 
                    __html: post.content.replace(/\n/g, '<br/>').replace(/#{1,6}\s/g, '<h2 class="text-white font-bold text-2xl mt-8 mb-4">').replace(/<h2[^>]*>/g, '<h2 class="text-white font-bold text-2xl mt-8 mb-4">') 
                  }}
                />
              </div>

              {/* Tags */}
              <div className="mt-8 pt-8 border-t border-gray-800">
                <h3 className="text-white font-semibold mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <Link
                      key={tag}
                      href={`/blog?tag=${tag}`}
                      className="px-3 py-1 bg-gray-800 hover:bg-teal-600 text-gray-300 hover:text-white rounded transition-colors text-sm"
                    >
                      #{tag}
                    </Link>
                  ))}
                </div>
              </div>

              {/* Social Actions */}
              <div className="mt-8 pt-8 border-t border-gray-800 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button
                    onClick={handleLike}
                    className={`flex items-center gap-2 px-4 py-2 rounded transition-colors ${
                      isLiked 
                        ? 'bg-red-600 text-white' 
                        : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd"/>
                    </svg>
                    {likes}
                  </button>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-gray-400 text-sm mr-2">Share:</span>
                  <button
                    onClick={() => handleShare('twitter')}
                    className="p-2 bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white rounded transition-colors"
                    title="Share on Twitter"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                  </button>
                  <button
                    onClick={() => handleShare('facebook')}
                    className="p-2 bg-gray-800 hover:bg-blue-700 text-gray-300 hover:text-white rounded transition-colors"
                    title="Share on Facebook"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  </button>
                  <button
                    onClick={() => handleShare('linkedin')}
                    className="p-2 bg-gray-800 hover:bg-blue-800 text-gray-300 hover:text-white rounded transition-colors"
                    title="Share on LinkedIn"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </button>
                  <button
                    onClick={() => handleShare('copy')}
                    className="p-2 bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white rounded transition-colors"
                    title="Copy Link"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </article>

          {/* Sidebar */}
          <aside className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Table of Contents */}
              <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6">
                <h3 className="text-white font-semibold mb-4">Table of Contents</h3>
                <nav className="space-y-2 text-sm">
                  <a href="#" className="block text-gray-400 hover:text-teal-400 transition-colors">
                    What is Modern Interior Design?
                  </a>
                  <a href="#" className="block text-gray-400 hover:text-teal-400 transition-colors">
                    Key Elements of Modern Design
                  </a>
                  <a href="#" className="block text-gray-400 hover:text-teal-400 transition-colors">
                    Tips for Creating a Modern Space
                  </a>
                </nav>
              </div>

              {/* Author Bio */}
              <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-6">
                <h3 className="text-white font-semibold mb-4">About the Author</h3>
                <div className="flex items-center gap-3 mb-3">
                  <img
                    src={post.author.avatar}
                    alt={post.author.name}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <p className="text-white font-medium">{post.author.name}</p>
                  </div>
                </div>
                <p className="text-gray-400 text-sm">{post.author.bio}</p>
              </div>
            </div>
          </aside>
        </div>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <section className="mt-16">
            <h2 className="text-3xl font-bold text-white mb-8">Related Posts</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <Link
                  key={relatedPost.id}
                  href={`/blog/${relatedPost.slug}`}
                  className="bg-gray-900/50 rounded-2xl border border-gray-800 overflow-hidden hover:border-teal-500 transition-all duration-300 group"
                >
                  {relatedPost.featuredImage && (
                    <img
                      src={relatedPost.featuredImage}
                      alt={relatedPost.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  )}
                  <div className="p-6">
                    <h3 className="text-white font-semibold mb-2 group-hover:text-teal-400 transition-colors">
                      {relatedPost.title}
                    </h3>
                    <p className="text-gray-400 text-sm mb-3">
                      {relatedPost.excerpt.substring(0, 100)}...
                    </p>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>{getRelativeTime(relatedPost.publishedAt)}</span>
                      <span>•</span>
                      <span>{relatedPost.readingTime} min read</span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </section>
        )}
      </div>
    </main>
  );
}
